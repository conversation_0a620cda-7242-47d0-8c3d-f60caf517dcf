#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海洋波浪数据分析器
分析BBXDR.txt文件中的波浪数据并生成可视化图表
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime
import re
import matplotlib.dates as mdates
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class WaveDataAnalyzer:
    def __init__(self, filename='BBXDR.txt'):
        """初始化分析器"""
        # 智能查找文件路径
        self.filename = self._find_data_file(filename)
        # 设置输出目录
        self.output_dir = self._get_output_dir()
        self.data = []
        self.parsed_data = {}

    def _find_data_file(self, filename):
        """智能查找数据文件的路径"""
        # 可能的文件路径
        possible_paths = [
            filename,  # 当前目录
            os.path.join('output', filename),  # output子目录
            os.path.join('..', 'output', filename),  # 上级目录的output子目录
        ]

        for path in possible_paths:
            if os.path.exists(path):
                print(f"找到数据文件: {path}")
                return path

        # 如果都找不到，返回原始文件名（会在load_data中报错）
        return filename

    def _get_output_dir(self):
        """确定输出目录"""
        # 如果数据文件在output目录中，输出也放在output目录
        if 'output' in self.filename:
            return 'output'
        # 否则输出到当前目录
        return '.'
        
    def parse_bbxdr_line(self, line):
        """解析BBXDR格式的数据行"""
        # 提取时间戳
        time_match = re.match(r'(\d{2}:\d{2}:\d{2}:\d{3})', line)
        if not time_match:
            return None
            
        timestamp = time_match.group(1)
        
        # 提取BBXDR数据
        bbxdr_match = re.search(r'\$BBXDR,([^*]+)', line)
        if not bbxdr_match:
            return None
            
        bbxdr_data = bbxdr_match.group(1).split(',')
        if len(bbxdr_data) < 4:
            return None
            
        data_type = bbxdr_data[0]  # D, F, G, A, N
        value = bbxdr_data[1]
        unit = bbxdr_data[2] if len(bbxdr_data) > 2 else ''
        parameter = bbxdr_data[3] if len(bbxdr_data) > 3 else ''
        
        try:
            value = float(value)
        except ValueError:
            return None
            
        return {
            'timestamp': timestamp,
            'data_type': data_type,
            'value': value,
            'unit': unit,
            'parameter': parameter
        }
    
    def load_data(self):
        """加载并解析数据文件"""
        print(f"正在加载数据文件: {self.filename}")
        
        try:
            with open(self.filename, 'r', encoding='utf-8') as file:
                lines = file.readlines()
        except FileNotFoundError:
            print(f"错误: 找不到文件 {self.filename}")
            return False
        except Exception as e:
            print(f"错误: 读取文件时出现问题 - {e}")
            return False
        
        for line in lines:
            parsed = self.parse_bbxdr_line(line.strip())
            if parsed:
                self.data.append(parsed)
        
        print(f"成功解析 {len(self.data)} 条数据记录")
        return True
    
    def organize_data(self):
        """整理数据到不同的参数类别"""
        if not self.data:
            print("错误: 没有数据可以整理")
            return False
        
        # 按参数类型分组数据
        for record in self.data:
            param = record['parameter']
            if param not in self.parsed_data:
                self.parsed_data[param] = {
                    'timestamps': [],
                    'values': [],
                    'unit': record['unit'],
                    'data_type': record['data_type']
                }
            
            self.parsed_data[param]['timestamps'].append(record['timestamp'])
            self.parsed_data[param]['values'].append(record['value'])
        
        print(f"数据已按 {len(self.parsed_data)} 个参数类型分组")
        return True
    
    def convert_timestamps(self, timestamps):
        """将时间戳字符串转换为datetime对象"""
        datetime_objects = []
        base_date = "2024-01-01"  # 假设基准日期
        
        for ts in timestamps:
            try:
                # 格式: HH:MM:SS:mmm
                time_parts = ts.split(':')
                if len(time_parts) == 4:
                    hour, minute, second, millisecond = time_parts
                    dt_str = f"{base_date} {hour}:{minute}:{second}.{millisecond}"
                    dt = datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S.%f")
                    datetime_objects.append(dt)
            except ValueError:
                continue
        
        return datetime_objects
    
    def plot_wave_parameters(self):
        """绘制主要波浪参数图表"""
        if not self.parsed_data:
            print("错误: 没有数据可以绘制")
            return
        
        # 选择主要的波浪参数
        wave_params = {
            'AP_WAVE_LENGTH': '波长 (m)',
            'AP_WAVE_PERIOD': '波浪周期 (s)',
            'AP_WAVE_ANGLE': '波浪角度 (度)',
            'AP_WAVE_SPEED': '波浪速度 (m/s)'
        }
        
        available_params = {k: v for k, v in wave_params.items() if k in self.parsed_data}
        
        if not available_params:
            print("警告: 没有找到主要波浪参数数据")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('海洋波浪参数分析', fontsize=16, fontweight='bold')
        
        axes = axes.flatten()
        
        for i, (param, label) in enumerate(available_params.items()):
            if i >= 4:
                break
                
            timestamps = self.convert_timestamps(self.parsed_data[param]['timestamps'])
            values = self.parsed_data[param]['values']
            
            # 确保时间戳和数值数量匹配
            min_len = min(len(timestamps), len(values))
            timestamps = timestamps[:min_len]
            values = values[:min_len]
            
            if timestamps and values:
                axes[i].plot(timestamps, values, 'b-', linewidth=1.5, alpha=0.7)
                axes[i].set_title(label, fontsize=12, fontweight='bold')
                axes[i].set_ylabel('数值')
                axes[i].grid(True, alpha=0.3)
                
                # 格式化x轴时间显示
                axes[i].xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
                axes[i].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        output_path = os.path.join(self.output_dir, 'wave_parameters.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"波浪参数图表已保存为 {output_path}")
    
    def plot_sensor_data(self):
        """绘制传感器数据图表"""
        if not self.parsed_data:
            print("错误: 没有数据可以绘制")
            return
        
        # 选择传感器数据参数
        sensor_params = {
            'DRG1': '陀螺仪数据1 (m)',
            'DRT1': '陀螺仪数据2 (m)', 
            'DAV1': '平均数据 (m)',
            'SRT1': '传感器读数 (H)'
        }
        
        available_sensors = {k: v for k, v in sensor_params.items() if k in self.parsed_data}
        
        if not available_sensors:
            print("警告: 没有找到传感器数据")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('传感器数据分析', fontsize=16, fontweight='bold')
        
        axes = axes.flatten()
        
        for i, (param, label) in enumerate(available_sensors.items()):
            if i >= 4:
                break
                
            timestamps = self.convert_timestamps(self.parsed_data[param]['timestamps'])
            values = self.parsed_data[param]['values']
            
            min_len = min(len(timestamps), len(values))
            timestamps = timestamps[:min_len]
            values = values[:min_len]
            
            if timestamps and values:
                axes[i].plot(timestamps, values, 'r-', linewidth=1.5, alpha=0.7)
                axes[i].set_title(label, fontsize=12, fontweight='bold')
                axes[i].set_ylabel('数值')
                axes[i].grid(True, alpha=0.3)
                
                axes[i].xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
                axes[i].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        output_path = os.path.join(self.output_dir, 'sensor_data.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"传感器数据图表已保存为 {output_path}")
    
    def generate_summary_statistics(self):
        """生成数据统计摘要"""
        if not self.parsed_data:
            print("错误: 没有数据可以分析")
            return
        
        print("\n=== 数据统计摘要 ===")
        
        for param, data in self.parsed_data.items():
            values = data['values']
            if values:
                print(f"\n{param}:")
                print(f"  数据点数量: {len(values)}")
                print(f"  最小值: {min(values):.4f}")
                print(f"  最大值: {max(values):.4f}")
                print(f"  平均值: {np.mean(values):.4f}")
                print(f"  标准差: {np.std(values):.4f}")
                print(f"  单位: {data['unit']}")
    
    def plot_correlation_analysis(self):
        """绘制参数相关性分析图"""
        if not self.parsed_data:
            print("错误: 没有数据可以分析")
            return

        # 选择数值变化的参数进行相关性分析
        variable_params = {}
        for param, data in self.parsed_data.items():
            values = data['values']
            if len(values) > 10 and np.std(values) > 0.001:  # 过滤掉常数值
                variable_params[param] = values

        if len(variable_params) < 2:
            print("警告: 没有足够的变化参数进行相关性分析")
            return

        # 创建相关性矩阵
        param_names = list(variable_params.keys())
        correlation_matrix = np.zeros((len(param_names), len(param_names)))

        for i, param1 in enumerate(param_names):
            for j, param2 in enumerate(param_names):
                values1 = variable_params[param1]
                values2 = variable_params[param2]
                min_len = min(len(values1), len(values2))
                if min_len > 1:
                    correlation_matrix[i, j] = np.corrcoef(values1[:min_len], values2[:min_len])[0, 1]

        # 绘制热力图
        fig, ax = plt.subplots(figsize=(10, 8))
        im = ax.imshow(correlation_matrix, cmap='coolwarm', vmin=-1, vmax=1)

        # 设置标签
        ax.set_xticks(np.arange(len(param_names)))
        ax.set_yticks(np.arange(len(param_names)))
        ax.set_xticklabels(param_names, rotation=45, ha='right')
        ax.set_yticklabels(param_names)

        # 添加数值标注
        for i in range(len(param_names)):
            for j in range(len(param_names)):
                text = ax.text(j, i, f'{correlation_matrix[i, j]:.2f}',
                             ha="center", va="center", color="black", fontsize=8)

        ax.set_title("参数相关性分析热力图", fontsize=14, fontweight='bold')
        plt.colorbar(im, ax=ax, label='相关系数')
        plt.tight_layout()
        output_path = os.path.join(self.output_dir, 'correlation_analysis.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"相关性分析图表已保存为 {output_path}")

    def plot_time_series_overview(self):
        """绘制时间序列总览图"""
        if not self.parsed_data:
            print("错误: 没有数据可以绘制")
            return

        # 选择主要的变化参数
        main_params = ['DRG1', 'DRT1', 'DAV1', 'AP_WAVE_ANGLE']
        available_params = [p for p in main_params if p in self.parsed_data]

        if not available_params:
            print("警告: 没有找到主要参数数据")
            return

        fig, axes = plt.subplots(len(available_params), 1, figsize=(15, 3*len(available_params)))
        if len(available_params) == 1:
            axes = [axes]

        fig.suptitle('时间序列数据总览', fontsize=16, fontweight='bold')

        for i, param in enumerate(available_params):
            timestamps = self.convert_timestamps(self.parsed_data[param]['timestamps'])
            values = self.parsed_data[param]['values']

            min_len = min(len(timestamps), len(values))
            timestamps = timestamps[:min_len]
            values = values[:min_len]

            if timestamps and values:
                axes[i].plot(timestamps, values, 'b-', linewidth=1.5, alpha=0.8)
                axes[i].fill_between(timestamps, values, alpha=0.3)
                axes[i].set_title(f'{param} - {self.parsed_data[param]["unit"]}', fontsize=12)
                axes[i].set_ylabel('数值')
                axes[i].grid(True, alpha=0.3)
                axes[i].xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))

                # 添加统计信息
                mean_val = np.mean(values)
                std_val = np.std(values)
                axes[i].axhline(y=mean_val, color='r', linestyle='--', alpha=0.7, label=f'平均值: {mean_val:.3f}')
                axes[i].axhline(y=mean_val+std_val, color='orange', linestyle=':', alpha=0.7, label=f'+1σ: {mean_val+std_val:.3f}')
                axes[i].axhline(y=mean_val-std_val, color='orange', linestyle=':', alpha=0.7, label=f'-1σ: {mean_val-std_val:.3f}')
                axes[i].legend(loc='upper right', fontsize=8)

        plt.tight_layout()
        output_path = os.path.join(self.output_dir, 'time_series_overview.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"时间序列总览图表已保存为 {output_path}")

    def generate_detailed_report(self):
        """生成详细的分析报告"""
        if not self.parsed_data:
            print("错误: 没有数据可以分析")
            return

        report_filename = os.path.join(self.output_dir, 'wave_analysis_report.txt')

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("海洋波浪数据分析报告\n")
            f.write("=" * 60 + "\n\n")

            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据文件: {self.filename}\n")
            f.write(f"总数据记录数: {len(self.data)}\n")
            f.write(f"参数类型数量: {len(self.parsed_data)}\n\n")

            f.write("详细参数统计:\n")
            f.write("-" * 40 + "\n")

            for param, data in self.parsed_data.items():
                values = data['values']
                if values:
                    f.write(f"\n{param}:\n")
                    f.write(f"  单位: {data['unit']}\n")
                    f.write(f"  数据类型: {data['data_type']}\n")
                    f.write(f"  数据点数量: {len(values)}\n")
                    f.write(f"  最小值: {min(values):.6f}\n")
                    f.write(f"  最大值: {max(values):.6f}\n")
                    f.write(f"  平均值: {np.mean(values):.6f}\n")
                    f.write(f"  中位数: {np.median(values):.6f}\n")
                    f.write(f"  标准差: {np.std(values):.6f}\n")
                    f.write(f"  变异系数: {np.std(values)/np.mean(values)*100:.2f}%\n")

                    # 计算变化趋势
                    if len(values) > 1:
                        trend = "上升" if values[-1] > values[0] else "下降"
                        f.write(f"  总体趋势: {trend}\n")

            f.write("\n" + "=" * 60 + "\n")
            f.write("分析结论:\n")
            f.write("-" * 20 + "\n")

            # 波浪参数分析
            if 'AP_WAVE_PERIOD' in self.parsed_data:
                period = self.parsed_data['AP_WAVE_PERIOD']['values'][0]
                f.write(f"波浪周期: {period:.2f} 秒\n")

            if 'AP_WAVE_LENGTH' in self.parsed_data:
                length = self.parsed_data['AP_WAVE_LENGTH']['values'][0]
                f.write(f"波浪长度: {length:.2f} 米\n")

            if 'AP_WAVE_SPEED' in self.parsed_data:
                speed = self.parsed_data['AP_WAVE_SPEED']['values'][0]
                f.write(f"波浪速度: {speed:.2f} m/s\n")

            if 'AP_WAVE_ANGLE' in self.parsed_data:
                angles = self.parsed_data['AP_WAVE_ANGLE']['values']
                f.write(f"波浪角度变化范围: {min(angles):.1f}° - {max(angles):.1f}°\n")
                f.write(f"平均波浪角度: {np.mean(angles):.1f}°\n")

        print(f"详细分析报告已保存为 {report_filename}")

    def run_analysis(self):
        """运行完整的数据分析流程"""
        print("开始海洋波浪数据分析...")

        if not self.load_data():
            return False

        if not self.organize_data():
            return False

        self.generate_summary_statistics()
        self.plot_wave_parameters()
        self.plot_sensor_data()
        self.plot_correlation_analysis()
        self.plot_time_series_overview()
        self.generate_detailed_report()

        print("\n分析完成！所有图表和报告已生成并保存。")
        return True

def main():
    """主函数"""
    analyzer = WaveDataAnalyzer('BBXDR.txt')
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
