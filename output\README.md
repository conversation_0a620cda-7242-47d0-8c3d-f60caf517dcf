# 海洋波浪数据分析器

这是一个用于分析BBXDR格式海洋波浪数据的Python程序，使用matplotlib生成可视化图表。

## 功能特性

- 📊 **多种图表类型**：波浪参数图、传感器数据图、相关性分析热力图、时间序列总览图
- 📈 **统计分析**：详细的数据统计摘要，包括均值、标准差、变异系数等
- 📝 **分析报告**：自动生成详细的文本分析报告
- 🎨 **中文支持**：完整的中文界面和图表标签

## 生成的文件

运行程序后会生成以下文件：

### 图表文件 (PNG格式)
1. **wave_parameters.png** - 波浪参数分析图
   - 波长 (m)
   - 波浪周期 (s) 
   - 波浪角度 (度)
   - 波浪速度 (m/s)

2. **sensor_data.png** - 传感器数据分析图
   - 陀螺仪数据1 (DRG1)
   - 陀螺仪数据2 (DRT1)
   - 平均数据 (DAV1)
   - 传感器读数 (SRT1)

3. **correlation_analysis.png** - 参数相关性分析热力图
   - 显示各参数之间的相关系数
   - 颜色编码：红色表示正相关，蓝色表示负相关

4. **time_series_overview.png** - 时间序列总览图
   - 显示主要参数随时间的变化趋势
   - 包含统计线（平均值、±1σ）

### 报告文件
- **wave_analysis_report.txt** - 详细的数据分析报告
  - 包含所有参数的统计信息
  - 波浪特征分析结论

## 使用方法

### 1. 环境要求
确保已安装以下Python包：
```bash
pip install matplotlib pandas numpy
```

### 2. 运行分析
```bash
python wave_data_analyzer.py
```

### 3. 数据格式
程序分析BBXDR.txt文件，支持以下格式的数据：
```
15:02:16:001 -> $BBXDR,D,0.12057,M,DRG1*08
15:02:16:001 -> $BBXDR,G,17.34196,M,AP_WAVE_LENGTH*5D
```

## 数据参数说明

### 波浪参数
- **AP_WAVE_LENGTH**: 波浪长度 (米)
- **AP_WAVE_PERIOD**: 波浪周期 (秒)
- **AP_WAVE_ANGLE**: 波浪角度 (度)
- **AP_WAVE_SPEED**: 波浪速度 (m/s)
- **AP_WAVE_DIR**: 波浪方向

### 传感器数据
- **DRG1/DRT1**: 陀螺仪数据 (米)
- **DAV1**: 平均数据 (米)
- **SRT1**: 传感器读数
- **ABI1**: 压力数据
- **FAV1/FRT1**: 频率数据

## 分析结果解读

### 统计指标
- **平均值**: 数据的中心趋势
- **标准差**: 数据的离散程度
- **变异系数**: 相对变异程度 (标准差/平均值 × 100%)
- **相关系数**: 参数间的线性关系强度 (-1到1)

### 图表解读
1. **时间序列图**: 观察参数随时间的变化趋势
2. **相关性热力图**: 识别参数间的关联关系
3. **统计线**: 帮助识别异常值和数据分布

## 技术特点

- 🔧 **自动数据解析**: 智能解析BBXDR格式数据
- 📊 **多维度分析**: 时间序列、统计分析、相关性分析
- 🎨 **美观图表**: 高质量的matplotlib图表，支持中文显示
- 📄 **详细报告**: 自动生成包含所有分析结果的文本报告

## 文件结构
```
output/
├── BBXDR.txt                    # 原始数据文件
├── wave_data_analyzer.py        # 主程序文件
├── wave_parameters.png          # 波浪参数图表
├── sensor_data.png              # 传感器数据图表
├── correlation_analysis.png     # 相关性分析图表
├── time_series_overview.png     # 时间序列总览图表
├── wave_analysis_report.txt     # 详细分析报告
└── README.md                    # 使用说明文档
```

## 注意事项

1. 确保BBXDR.txt文件与程序在同一目录下
2. 程序会自动覆盖之前生成的图表和报告文件
3. 如果数据中某些参数为常数值，相关性分析会自动过滤这些参数
4. 图表会自动显示并保存为PNG格式，分辨率为300 DPI

---
*海洋波浪数据分析器 - 专业的海洋数据可视化工具*
